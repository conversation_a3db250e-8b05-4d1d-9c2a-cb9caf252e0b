<template>
  <div class="calender-config">
    <!-- 查询条件 -->
    <div class="grid-form">
      <a-form layout="vertical" size="small">
        <div class="form-row">
          <a-form-item label="年度">
            <a-date-picker
              v-model:value="queryForm.year"
              picker="year"
              placeholder="选择年"
              :allowClear="false"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="供应商">
            <a-select
              v-model:value="queryForm.supplierCode"
              placeholder="请选择供应商"
              show-search
              :filter-option="filterSupplierOption"
            >
              <a-select-option v-for="item in supplierList" :key="item.supplierCode" :value="item.supplierCode">
                {{ item.supplierName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </a-form>
    </div>

    <!-- 主要内容区域 -->
    <div class="content" v-if="queryForm.year && queryForm.supplierCode">
      <!-- 左侧：选择月份 -->
      <div class="part left">
        <div class="section-header">
          <h3>选择月份</h3>
        </div>
        <a-spin :spinning="monthsLoading">
          <div class="months-grid">
            <div
              v-for="month in monthsList"
              :key="month.month"
              class="month-item"
              :class="{ active: selectedMonth === month.month }"
              @click="onSelectMonth(month.month)"
            >
              <div class="month-name">{{ month.monthName }}</div>
              <div class="work-days">({{ month.workDays === '-' ? '-' : month.workDays }}天)</div>
            </div>
          </div>
          <div class="last-modified-time">
            <div class="time-content">
              <ClockCircleOutlined />
              <span>最后更新：{{ lastModifiedTime || '无' }}</span>
            </div>
          </div>
        </a-spin>
      </div>

      <!-- 右侧：展示有效天数 -->
      <div class="part right">
        <div class="section-header">
          <div class="header-left">
            <h3>{{ selectedMonth ? getMonthName(selectedMonth) : '请选择月份' }}</h3>
            <div class="legend">
              <span class="legend-item">
                <span class="legend-color work-day"></span>
                有效
              </span>
              <span class="legend-item">
                <span class="legend-color holiday"></span>
                无效
              </span>
            </div>
          </div>
          <div class="header-right">
            <a-button type="primary" @click="saveCalendar" :loading="saveLoading"> 保存 </a-button>
          </div>
        </div>

        <div v-if="selectedMonth" class="calendar-container">
          <a-spin :spinning="daysLoading">
            <div class="calendar-grid">
              <div class="calendar-header">
                <div class="day-header">日</div>
                <div class="day-header">一</div>
                <div class="day-header">二</div>
                <div class="day-header">三</div>
                <div class="day-header">四</div>
                <div class="day-header">五</div>
                <div class="day-header">六</div>
              </div>
              <div class="calendar-body">
                <div
                  v-for="day in calendarDays"
                  :key="day.date"
                  class="day-cell"
                  :class="getDayClass(day)"
                  @click="toggleDayStatus(day)"
                >
                  <span class="day-number">{{ day.day }}</span>
                </div>
              </div>
            </div>
          </a-spin>
        </div>

        <div v-else class="empty-state">
          <a-empty description="请先选择月份" />
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state-main">
      <a-empty description="请选择年度和供应商" />
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import { getSuppliers } from '@/api/weekly'
  import { getYearMonths, getMonthDays, saveOrUpdateSupplierWorkCalendar } from './api'

  // 响应式数据
  const queryForm = reactive({
    year: dayjs(), // 当前年份的 dayjs 对象
    supplierCode: '',
  })

  const supplierList = ref([])
  const monthsList = ref([])
  const selectedMonth = ref('')
  const calendarDays = ref([])
  const originalDaysData = ref([]) // 保存原始数据用于比较变更

  // 加载状态
  const monthsLoading = ref(false)
  const daysLoading = ref(false)
  const saveLoading = ref(false)

  // 计算属性
  const yearString = computed(() => {
    return queryForm.year ? dayjs(queryForm.year).format('YYYY') : ''
  })

  // 月份名称映射
  const monthNames = {
    '01': '一月',
    '02': '二月',
    '03': '三月',
    '04': '四月',
    '05': '五月',
    '06': '六月',
    '07': '七月',
    '08': '八月',
    '09': '九月',
    10: '十月',
    11: '十一月',
    12: '十二月',
  }

  // 方法
  const filterSupplierOption = (input, option) => {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  const getMonthName = month => {
    const monthNum = month.split('-')[1]
    return monthNames[monthNum] || month
  }

  const getDayClass = day => {
    const classes = ['day-item']

    if (day.isCurrentMonth === false) {
      classes.push('other-month')
      return classes
    }

    if (day.isHoliday === '0') {
      classes.push('work-day')
    } else {
      classes.push('holiday')
    }

    if (day.isToday) {
      classes.push('today')
    }

    return classes
  }

  // 事件处理

  const onSelectMonth = month => {
    selectedMonth.value = month
    loadMonthDays(month)
  }

  const toggleDayStatus = day => {
    if (day.isCurrentMonth === false) return

    day.isHoliday = day.isHoliday === '0' ? '1' : '0'
  }

  // API调用
  const loadSupplierList = async () => {
    try {
      const suppliers = await getSuppliers()
      supplierList.value = suppliers || []

      if (suppliers && suppliers.length > 0) {
        queryForm.supplierCode = suppliers[0].supplierCode
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error)
      message.error('获取供应商列表失败')
    }
  }

  const loadMonthsList = async () => {
    if (!queryForm.supplierCode || !yearString.value) return

    monthsLoading.value = true
    try {
      const params = {
        supplierCode: queryForm.supplierCode,
        year: yearString.value,
      }

      const response = await getYearMonths(params)
      const result = response?.supplierWorkDayByYearDTOList || []
      console.log('response', response)
      // 生成12个月的数据，基于接口返回的数据
      const months = []
      for (let i = 1; i <= 12; i++) {
        const monthStr = i.toString().padStart(2, '0')
        const monthKey = `${yearString.value}-${monthStr}`
        const monthData = result.find(item => item.dateMonth === monthKey)
        console.log('monthData', monthData)
        months.push({
          month: monthKey,
          monthName: monthNames[monthStr],
          workDays: monthData ? monthData.workDayNum : '-',
        })
      }
      console.log('months', months)
      monthsList.value = months

      // 自动选择第一个月份
      if (months.length > 0 && !selectedMonth.value) {
        selectedMonth.value = months[0].month
        loadMonthDays(months[0].month)
      }
    } catch (error) {
      console.error('获取月份数据失败:', error)
      message.error('获取月份数据失败')
      // 错误时也要显示12个月，但工作天数为 '-'
      const months = []
      for (let i = 1; i <= 12; i++) {
        const monthStr = i.toString().padStart(2, '0')
        const monthKey = `${yearString.value}-${monthStr}`
        months.push({
          month: monthKey,
          monthName: monthNames[monthStr],
          workDays: '-',
        })
      }
      monthsList.value = months
    } finally {
      monthsLoading.value = false
    }
  }

  const loadMonthDays = async month => {
    if (!queryForm.supplierCode || !yearString.value || !month) return

    daysLoading.value = true
    try {
      const params = {
        supplierCode: queryForm.supplierCode,
        year: yearString.value,
        month: month,
      }

      const response = await getMonthDays(params)
      // 根据实际接口返回结构处理数据：
      // { data: { workDayList: [{ "dateDay": "2025-01-01", "isHoliday": "1" }] } }
      const result = response?.workDayList || []
      originalDaysData.value = result
      console.log('response', response)
      console.log('month, result', month, result)
      // 生成日历数据
      generateCalendarDays(month, result)
    } catch (error) {
      console.error('获取月份天数失败:', error)
      message.error('获取月份天数失败')
    } finally {
      daysLoading.value = false
    }
  }

  const generateCalendarDays = (month, daysData) => {
    const [year, monthNum] = month.split('-')
    const firstDay = dayjs(`${year}-${monthNum}-01`)
    const lastDay = firstDay.endOf('month')
    const startDate = firstDay.startOf('week')
    const endDate = lastDay.endOf('week')

    const days = []
    let current = startDate

    while (current.isBefore(endDate) || current.isSame(endDate, 'day')) {
      const dateStr = current.format('YYYY-MM-DD')
      const dayData = daysData.find(d => d.dateDay === dateStr)
      const isCurrentMonth = current.month() === firstDay.month()

      days.push({
        date: dateStr,
        day: current.date(),
        isCurrentMonth,
        isToday: current.isSame(dayjs(), 'day'),
        isHoliday: dayData ? dayData.isHoliday : isCurrentMonth ? '1' : '1', // 使用接口数据，没有数据时默认为假期
      })

      current = current.add(1, 'day')
    }

    calendarDays.value = days
  }

  const saveCalendar = async () => {
    saveLoading.value = true
    try {
      // 保存当前月份的所有天数状态
      const adjustDayList = []

      calendarDays.value.forEach(day => {
        if (day.isCurrentMonth === false) return

        adjustDayList.push({
          dateDay: day.date,
          isHoliday: day.isHoliday,
        })
      })

      const params = {
        supplierCode: queryForm.supplierCode,
        year: yearString.value,
        month: selectedMonth.value,
        adjustDayList,
      }

      await saveOrUpdateSupplierWorkCalendar(params)
      message.success('保存成功')

      // 重新加载数据
      await loadMonthDays(selectedMonth.value)
      await loadMonthsList()
    } catch (error) {
      console.error('保存失败:', error)
      message.error('保存失败')
    } finally {
      saveLoading.value = false
    }
  }

  // 监听年份和供应商变化，自动加载月份数据
  watch(
    [() => queryForm.year, () => queryForm.supplierCode],
    ([newYear, newSupplierCode]) => {
      if (newYear && newSupplierCode) {
        console.log('年份和供应商变化，自动加载月份数据')
        selectedMonth.value = '' // 清空选中的月份
        calendarDays.value = [] // 清空日历数据
        loadMonthsList()
      }
    },
    { immediate: false }
  )

  // 监听年份和供应商变化，自动加载月份数据
  watch([() => queryForm.year, () => queryForm.supplierCode], ([newYear, newSupplierCode]) => {
    if (newYear && newSupplierCode) {
      selectedMonth.value = '' // 清空选中的月份
      calendarDays.value = [] // 清空日历数据
      loadMonthsList()
    }
  })

  // 生命周期
  onMounted(() => {
    loadSupplierList()
  })
</script>

<style lang="less" scoped>
  .calender-config {
    .grid-form {
      padding: 16px;
      background: #fff;
      margin-bottom: 16px;
      border-radius: 8px;

      .form-row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 24px;
      }

      :deep(.ant-form-item-label) {
        padding-bottom: 4px;
      }
    }

    .content {
      display: flex;
      gap: 16px;
      height: 700px;

      .part {
        background: #fff;
        border-radius: 8px;
        padding: 8px;
        flex: 1;
        display: flex;
        flex-direction: column;

        &.left {
          max-width: 50%;
        }

        &.right {
          max-width: 50%;
        }
      }
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }

        .legend {
          display: flex;
          align-items: center;
          gap: 12px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;

              &.work-day {
                background-color: #cdfee1;
              }

              &.holiday {
                background-color: #ffffff;
                border: 1px solid #d9d9d9;
              }
            }
          }
        }
      }

      .header-right {
        display: flex;
        align-items: center;
      }
    }

    .months-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      flex: 1;
      align-content: start;

      .month-item {
        padding: 12px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        min-height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        &:hover {
          border-color: #1890ff;
          background-color: #f0f8ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        &.active {
          border-color: #1890ff;
          background-color: #e6f7ff;
          color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }

        .month-name {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .work-days {
          font-size: 13px;
          color: #666;
        }
      }
    }

    .calendar-container {
      // flex: 1;
      display: flex;
      flex-direction: column;

      .calendar-grid {
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        overflow: hidden;
        flex: 1;
        display: flex;
        flex-direction: column;

        .calendar-header {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          background-color: #fafafa;

          .day-header {
            padding: 12px 8px;
            text-align: center;
            font-weight: 500;
            border-right: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-right: none;
            }
          }
        }

        .calendar-body {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          flex: 1;

          .day-cell {
            position: relative;
            border-right: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.2s;
            height: 60px;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            display: flex;
            align-items: center;
            justify-content: center;

            &:nth-child(7n) {
              border-right: none;
            }

            .day-number {
              margin: 8px;
              font-size: 14px;
              font-weight: 500;
            }

            &.other-month {
              background-color: #fafafa;
              color: #ccc;
              cursor: not-allowed;
            }

            &.work-day {
              background-color: #cdfee1;
              color: #333;

              &:hover {
                background-color: #b7f5d1;
              }
            }

            &.holiday {
              background-color: #ffffff;
              color: #333;

              &:hover {
                background-color: #f5f5f5;
              }
            }

            &.today {
              .day-number {
                background-color: #1890ff;
                color: white;
                border-radius: 50%;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 600;
              }
            }
          }
        }
      }
    }

    .empty-state,
    .empty-state-main {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      background: #fff;
      border-radius: 8px;
    }

    .empty-state-main {
      min-height: 500px;
    }
  }
</style>
